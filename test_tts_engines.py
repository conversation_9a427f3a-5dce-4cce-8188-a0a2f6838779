#!/usr/bin/env python3
"""
测试不同TTS引擎的功能
"""

import os
import sys
import tempfile
from uuid import uuid4

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.config import config
from app.services import voice
from app.utils import utils

def test_edge_tts():
    """测试Edge TTS"""
    print("\n=== 测试 Edge TTS ===")
    
    # 设置测试参数
    text = "这是Edge TTS的测试语音，感谢使用NarratoAI"
    voice_name = "zh-CN-XiaoyiNeural"  # 移除-Female后缀
    voice_rate = 1.0
    voice_pitch = 1.0
    
    # 创建临时文件
    temp_dir = tempfile.mkdtemp()
    audio_file = os.path.join(temp_dir, f"edge_tts_test_{uuid4()}.mp3")
    
    try:
        print(f"语音文件: {audio_file}")
        print(f"文本: {text}")
        print(f"语音: {voice_name}")
        
        # 调用TTS
        sub_maker = voice.azure_tts_v1(text, voice_name, voice_rate, voice_pitch, audio_file)
        
        if sub_maker and os.path.exists(audio_file):
            file_size = os.path.getsize(audio_file)
            print(f"✅ Edge TTS 测试成功！文件大小: {file_size} bytes")
            return True
        else:
            print("❌ Edge TTS 测试失败")
            return False
            
    except Exception as e:
        print(f"❌ Edge TTS 测试出错: {str(e)}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(audio_file):
            os.remove(audio_file)
        os.rmdir(temp_dir)

def test_azure_speech():
    """测试Azure Speech Services"""
    print("\n=== 测试 Azure Speech Services ===")
    
    # 检查配置
    speech_key = config.azure.get("speech_key", "")
    speech_region = config.azure.get("speech_region", "")
    
    if not speech_key or not speech_region:
        print("⚠️ Azure Speech Services 未配置，跳过测试")
        print("请在配置文件中设置 speech_key 和 speech_region")
        return False
    
    # 设置测试参数
    text = "这是Azure Speech Services的测试语音，感谢使用NarratoAI"
    voice_name = "zh-CN-XiaoxiaoMultilingualNeural-V2"  # V2版本
    
    # 创建临时文件
    temp_dir = tempfile.mkdtemp()
    audio_file = os.path.join(temp_dir, f"azure_speech_test_{uuid4()}.mp3")
    
    try:
        print(f"语音文件: {audio_file}")
        print(f"文本: {text}")
        print(f"语音: {voice_name}")
        print(f"区域: {speech_region}")
        
        # 调用TTS
        sub_maker = voice.azure_tts_v2(text, voice_name, audio_file)
        
        if sub_maker and os.path.exists(audio_file):
            file_size = os.path.getsize(audio_file)
            print(f"✅ Azure Speech Services 测试成功！文件大小: {file_size} bytes")
            return True
        else:
            print("❌ Azure Speech Services 测试失败")
            return False
            
    except Exception as e:
        print(f"❌ Azure Speech Services 测试出错: {str(e)}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(audio_file):
            os.remove(audio_file)
        os.rmdir(temp_dir)

def test_soulvoice():
    """测试SoulVoice"""
    print("\n=== 测试 SoulVoice ===")
    
    # 检查配置
    api_key = config.soulvoice.get("api_key", "")
    voice_uri = config.soulvoice.get("voice_uri", "")
    
    if not api_key or not voice_uri:
        print("⚠️ SoulVoice 未配置，跳过测试")
        print("请在配置文件中设置 api_key 和 voice_uri")
        return False
    
    # 设置测试参数
    text = "这是SoulVoice的测试语音，感谢使用NarratoAI"
    voice_name = f"soulvoice:{voice_uri}"
    speed = 1.0
    
    # 创建临时文件
    temp_dir = tempfile.mkdtemp()
    audio_file = os.path.join(temp_dir, f"soulvoice_test_{uuid4()}.mp3")
    
    try:
        print(f"语音文件: {audio_file}")
        print(f"文本: {text}")
        print(f"语音URI: {voice_uri}")
        
        # 调用TTS
        sub_maker = voice.soulvoice_tts(text, voice_name, audio_file, speed)
        
        if sub_maker and os.path.exists(audio_file):
            file_size = os.path.getsize(audio_file)
            print(f"✅ SoulVoice 测试成功！文件大小: {file_size} bytes")
            return True
        else:
            print("❌ SoulVoice 测试失败")
            return False
            
    except Exception as e:
        print(f"❌ SoulVoice 测试出错: {str(e)}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(audio_file):
            os.remove(audio_file)
        os.rmdir(temp_dir)

def main():
    print("🎵 TTS引擎测试工具")
    print("=" * 50)
    
    # 显示当前配置
    print("\n📋 当前配置:")
    print(f"Azure Speech Key: {'已配置' if config.azure.get('speech_key') else '未配置'}")
    print(f"Azure Speech Region: {config.azure.get('speech_region', '未配置')}")
    print(f"SoulVoice API Key: {'已配置' if config.soulvoice.get('api_key') else '未配置'}")
    print(f"SoulVoice Voice URI: {config.soulvoice.get('voice_uri', '未配置')}")
    
    # 运行测试
    results = []
    
    # 测试Edge TTS（免费，应该总是可用）
    results.append(("Edge TTS", test_edge_tts()))
    
    # 测试Azure Speech Services（需要配置）
    results.append(("Azure Speech Services", test_azure_speech()))
    
    # 测试SoulVoice（需要配置）
    results.append(("SoulVoice", test_soulvoice()))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    for engine, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {engine}: {status}")
    
    successful_engines = [engine for engine, success in results if success]
    if successful_engines:
        print(f"\n🎉 可用的TTS引擎: {', '.join(successful_engines)}")
    else:
        print("\n⚠️ 没有可用的TTS引擎，请检查配置")

if __name__ == "__main__":
    main()
