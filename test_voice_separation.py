#!/usr/bin/env python3
"""
测试Edge TTS和Azure Speech Services的音色分区
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services import voice

def test_voice_separation():
    """测试音色列表分区"""
    print("🎵 测试Edge TTS和Azure Speech Services音色分区")
    print("=" * 60)
    
    # 获取所有音色
    support_locales = ["zh-CN", "en-US"]
    all_voices = voice.get_all_azure_voices(filter_locals=support_locales)
    
    # 分离Edge TTS和Azure Speech Services音色
    edge_voices = [v for v in all_voices if "-V2" not in v]
    azure_v2_voices = [v for v in all_voices if "-V2" in v]
    
    print(f"\n📊 音色统计:")
    print(f"总音色数量: {len(all_voices)}")
    print(f"Edge TTS音色数量: {len(edge_voices)}")
    print(f"Azure Speech Services V2音色数量: {len(azure_v2_voices)}")
    
    print(f"\n🎤 Edge TTS音色列表 (标准版本):")
    for i, voice_name in enumerate(edge_voices[:10], 1):  # 只显示前10个
        print(f"  {i:2d}. {voice_name}")
    if len(edge_voices) > 10:
        print(f"  ... 还有 {len(edge_voices) - 10} 个音色")
    
    print(f"\n🎤 Azure Speech Services音色列表 (V2版本):")
    for i, voice_name in enumerate(azure_v2_voices[:10], 1):  # 只显示前10个
        print(f"  {i:2d}. {voice_name}")
    if len(azure_v2_voices) > 10:
        print(f"  ... 还有 {len(azure_v2_voices) - 10} 个音色")
    
    # 验证分区正确性
    print(f"\n✅ 验证结果:")
    
    # 检查Edge TTS音色中是否有V2
    edge_has_v2 = any("-V2" in v for v in edge_voices)
    print(f"Edge TTS音色包含V2: {'❌ 是' if edge_has_v2 else '✅ 否'}")
    
    # 检查Azure V2音色中是否都有V2
    azure_all_v2 = all("-V2" in v for v in azure_v2_voices)
    print(f"Azure音色都是V2: {'✅ 是' if azure_all_v2 else '❌ 否'}")
    
    # 检查是否有重叠
    overlap = set(edge_voices) & set(azure_v2_voices)
    print(f"音色列表重叠: {'❌ 有重叠' if overlap else '✅ 无重叠'}")
    
    # 检查总数是否正确
    total_separated = len(edge_voices) + len(azure_v2_voices)
    print(f"分区总数正确: {'✅ 正确' if total_separated == len(all_voices) else '❌ 错误'}")
    
    print(f"\n🎯 推荐音色:")
    
    # 推荐中文女声
    zh_female_edge = [v for v in edge_voices if v.startswith("zh-CN") and "Female" in v]
    zh_female_azure = [v for v in azure_v2_voices if v.startswith("zh-CN") and "Female" in v]
    
    if zh_female_edge:
        print(f"Edge TTS中文女声推荐: {zh_female_edge[0]}")
    if zh_female_azure:
        print(f"Azure Speech Services中文女声推荐: {zh_female_azure[0]}")
    
    # 推荐中文男声
    zh_male_edge = [v for v in edge_voices if v.startswith("zh-CN") and "Male" in v]
    zh_male_azure = [v for v in azure_v2_voices if v.startswith("zh-CN") and "Male" in v]
    
    if zh_male_edge:
        print(f"Edge TTS中文男声推荐: {zh_male_edge[0]}")
    if zh_male_azure:
        print(f"Azure Speech Services中文男声推荐: {zh_male_azure[0]}")

def test_voice_detection():
    """测试语音类型检测函数"""
    print(f"\n🔍 测试语音类型检测:")
    
    test_voices = [
        "zh-CN-XiaoyiNeural-Female",  # Edge TTS
        "zh-CN-XiaoxiaoMultilingualNeural-V2-Female",  # Azure V2
        "soulvoice:speech:test",  # SoulVoice
    ]
    
    for test_voice in test_voices:
        is_soulvoice = voice.is_soulvoice_voice(test_voice)
        is_azure_v2 = voice.is_azure_v2_voice(test_voice)
        
        if is_soulvoice:
            engine_type = "SoulVoice"
        elif is_azure_v2:
            engine_type = "Azure Speech Services V2"
        else:
            engine_type = "Edge TTS"
        
        print(f"  {test_voice} -> {engine_type}")

def main():
    test_voice_separation()
    test_voice_detection()
    
    print(f"\n" + "=" * 60)
    print("🎉 音色分区测试完成！")

if __name__ == "__main__":
    main()
