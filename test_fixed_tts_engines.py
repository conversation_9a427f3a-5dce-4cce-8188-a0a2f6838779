#!/usr/bin/env python3
"""
测试修复后的TTS引擎音色分区
"""

import os
import sys
import tempfile
from uuid import uuid4

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.config import config
from app.services import voice
from app.utils import utils

def test_edge_tts_with_standard_voice():
    """测试Edge TTS使用标准音色"""
    print("\n=== 测试 Edge TTS (标准音色) ===")
    
    # 使用标准版本的音色（不带V2）
    text = "这是Edge TTS的测试语音，使用标准音色"
    voice_name = "zh-CN-XiaoxiaoNeural"  # 标准版本，不带V2
    voice_rate = 1.0
    voice_pitch = 1.0
    
    # 创建临时文件
    temp_dir = tempfile.mkdtemp()
    audio_file = os.path.join(temp_dir, f"edge_standard_test_{uuid4()}.mp3")
    
    try:
        print(f"语音: {voice_name} (标准版本)")
        print(f"文本: {text}")
        
        # 调用TTS
        sub_maker = voice.azure_tts_v1(text, voice_name, voice_rate, voice_pitch, audio_file)
        
        if sub_maker and os.path.exists(audio_file):
            file_size = os.path.getsize(audio_file)
            print(f"✅ Edge TTS 标准音色测试成功！文件大小: {file_size} bytes")
            return True
        else:
            print("❌ Edge TTS 标准音色测试失败")
            return False
            
    except Exception as e:
        print(f"❌ Edge TTS 标准音色测试出错: {str(e)}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(audio_file):
            os.remove(audio_file)
        os.rmdir(temp_dir)

def test_azure_speech_with_v2_voice():
    """测试Azure Speech Services使用V2音色"""
    print("\n=== 测试 Azure Speech Services (V2音色) ===")
    
    # 检查配置
    speech_key = config.azure.get("speech_key", "")
    speech_region = config.azure.get("speech_region", "")
    
    if not speech_key or not speech_region:
        print("⚠️ Azure Speech Services 未配置，跳过测试")
        return False
    
    # 使用V2版本的音色
    text = "这是Azure Speech Services的测试语音，使用V2音色"
    voice_name = "zh-CN-XiaoxiaoMultilingualNeural-V2"  # V2版本
    
    # 创建临时文件
    temp_dir = tempfile.mkdtemp()
    audio_file = os.path.join(temp_dir, f"azure_v2_test_{uuid4()}.mp3")
    
    try:
        print(f"语音: {voice_name} (V2版本)")
        print(f"文本: {text}")
        print(f"区域: {speech_region}")
        
        # 调用TTS
        sub_maker = voice.azure_tts_v2(text, voice_name, audio_file)
        
        if sub_maker and os.path.exists(audio_file):
            file_size = os.path.getsize(audio_file)
            print(f"✅ Azure Speech Services V2音色测试成功！文件大小: {file_size} bytes")
            return True
        else:
            print("❌ Azure Speech Services V2音色测试失败")
            return False
            
    except Exception as e:
        print(f"❌ Azure Speech Services V2音色测试出错: {str(e)}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(audio_file):
            os.remove(audio_file)
        os.rmdir(temp_dir)

def test_voice_routing():
    """测试语音路由是否正确"""
    print("\n=== 测试语音路由 ===")
    
    test_cases = [
        {
            "voice": "zh-CN-XiaoxiaoNeural-Female",
            "expected_engine": "Edge TTS (azure_tts_v1)",
            "is_v2": False,
            "is_soulvoice": False
        },
        {
            "voice": "zh-CN-XiaoxiaoMultilingualNeural-V2-Female", 
            "expected_engine": "Azure Speech Services (azure_tts_v2)",
            "is_v2": True,
            "is_soulvoice": False
        },
        {
            "voice": "soulvoice:speech:test",
            "expected_engine": "SoulVoice (soulvoice_tts)",
            "is_v2": False,
            "is_soulvoice": True
        }
    ]
    
    all_correct = True
    
    for case in test_cases:
        voice_name = case["voice"]
        expected_engine = case["expected_engine"]
        
        is_soulvoice = voice.is_soulvoice_voice(voice_name)
        is_azure_v2 = bool(voice.is_azure_v2_voice(voice_name))
        
        # 判断实际路由
        if is_soulvoice:
            actual_engine = "SoulVoice (soulvoice_tts)"
        elif is_azure_v2:
            actual_engine = "Azure Speech Services (azure_tts_v2)"
        else:
            actual_engine = "Edge TTS (azure_tts_v1)"
        
        is_correct = (actual_engine == expected_engine)
        status = "✅" if is_correct else "❌"
        
        print(f"  {status} {voice_name}")
        print(f"      期望: {expected_engine}")
        print(f"      实际: {actual_engine}")
        
        if not is_correct:
            all_correct = False
    
    return all_correct

def main():
    print("🎵 测试修复后的TTS引擎音色分区")
    print("=" * 60)
    
    # 显示当前配置
    print("\n📋 当前配置:")
    print(f"Azure Speech Key: {'已配置' if config.azure.get('speech_key') else '未配置'}")
    print(f"Azure Speech Region: {config.azure.get('speech_region', '未配置')}")
    print(f"SoulVoice API Key: {'已配置' if config.soulvoice.get('api_key') else '未配置'}")
    
    # 运行测试
    results = []
    
    # 测试语音路由
    routing_correct = test_voice_routing()
    results.append(("语音路由", routing_correct))
    
    # 测试Edge TTS标准音色
    edge_result = test_edge_tts_with_standard_voice()
    results.append(("Edge TTS (标准音色)", edge_result))
    
    # 测试Azure Speech Services V2音色
    azure_result = test_azure_speech_with_v2_voice()
    results.append(("Azure Speech Services (V2音色)", azure_result))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    successful_tests = [name for name, success in results if success]
    if len(successful_tests) == len(results):
        print(f"\n🎉 所有测试通过！音色分区修复成功！")
    else:
        print(f"\n⚠️ 部分测试失败，请检查配置")
    
    print(f"\n💡 音色分区说明:")
    print(f"  • Edge TTS: 使用标准版本音色 (不带-V2后缀)")
    print(f"  • Azure Speech Services: 使用V2版本音色 (带-V2后缀)")
    print(f"  • SoulVoice: 使用自定义音色URI")

if __name__ == "__main__":
    main()
