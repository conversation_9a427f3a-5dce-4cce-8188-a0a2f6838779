#!/usr/bin/env python3
"""
测试新的音频设置界面
"""

import streamlit as st
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from webui.components.audio_settings import (
    get_tts_engine_options,
    get_tts_engine_descriptions,
    render_edge_tts_settings,
    render_azure_speech_settings,
    render_soulvoice_engine_settings
)
from app.config import config

def tr(text):
    """简单的翻译函数"""
    translations = {
        "Audio Settings": "音频设置",
        "Speech Synthesis": "朗读声音",
        "Speech Volume": "朗读音量",
        "Speech Rate": "朗读速度",
        "Speech Pitch": "语调",
        "Play Voice": "试听语音合成",
        "Synthesizing Voice": "正在合成语音",
        "Voice synthesis successful": "语音合成成功",
        "Voice synthesis failed": "语音合成失败",
        "Voice Example": "语音示例",
        "Female": "女性",
        "Male": "男性",
        "Speech Region": "语音区域",
        "Speech Key": "语音密钥",
        "Adjust the volume of the original audio": "调节原声音量"
    }
    return translations.get(text, text)

def main():
    st.set_page_config(
        page_title="音频设置测试",
        page_icon="🎵",
        layout="wide"
    )
    
    st.title("🎵 音频设置界面测试")
    st.write("测试新的TTS引擎选择功能")
    
    # 初始化session state
    if "ui_language" not in st.session_state:
        st.session_state["ui_language"] = "zh-CN"
    
    # 显示当前配置
    with st.expander("当前配置信息", expanded=False):
        st.json({
            "TTS引擎": config.ui.get("tts_engine", "edge_tts"),
            "Azure配置": dict(config.azure),
            "SoulVoice配置": dict(config.soulvoice),
            "UI配置": dict(config.ui)
        })
    
    # 测试TTS引擎选择器
    st.header("1. TTS引擎选择器测试")
    
    engine_options = get_tts_engine_options()
    engine_descriptions = get_tts_engine_descriptions()
    
    st.write("可用的TTS引擎:")
    for key, name in engine_options.items():
        st.write(f"- {key}: {name}")
    
    # 测试引擎描述
    st.header("2. 引擎描述测试")
    for engine_id, desc in engine_descriptions.items():
        with st.expander(f"{desc['title']} 详细信息"):
            st.write(f"**特点:** {desc['features']}")
            st.write(f"**适用场景:** {desc['use_case']}")
            if desc['registration']:
                st.write(f"**注册地址:** {desc['registration']}")
    
    # 测试引擎配置界面
    st.header("3. 引擎配置界面测试")
    
    selected_engine = st.selectbox(
        "选择要测试的TTS引擎",
        options=list(engine_options.keys()),
        format_func=lambda x: engine_options[x]
    )
    
    st.subheader(f"测试 {engine_options[selected_engine]} 配置界面")
    
    if selected_engine == "edge_tts":
        render_edge_tts_settings(tr)
    elif selected_engine == "azure_speech":
        render_azure_speech_settings(tr)
    elif selected_engine == "soulvoice":
        render_soulvoice_engine_settings(tr)
    
    # 显示配置变化
    st.header("4. 配置变化监控")
    if st.button("刷新配置"):
        st.rerun()
    
    st.write("当前session_state中的语音相关配置:")
    voice_config = {k: v for k, v in st.session_state.items() if 'voice' in k.lower()}
    if voice_config:
        st.json(voice_config)
    else:
        st.write("暂无语音配置")

if __name__ == "__main__":
    main()
